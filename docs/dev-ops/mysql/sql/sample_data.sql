-- 示例数据插入脚本
-- 用于测试AI Agent系统

-- 1. 插入系统提示词
INSERT INTO ai_client_system_prompt (prompt_content) VALUES 
('你是一个专业的AI助手，请根据用户的问题提供准确、有帮助的回答。'),
('你是一个技术专家，专门回答关于Spring框架和Java开发的问题。'),
('你是一个友好的客服助手，请耐心回答用户的问题并提供优质的服务。');

-- 2. 插入AI模型配置
INSERT INTO ai_client_model (model_name, base_url, api_key, completions_path, embeddings_path, model_type, model_version, timeout) VALUES 
('gpt-3.5-turbo', 'https://api.openai.com', 'your-openai-api-key', '/v1/chat/completions', '/v1/embeddings', 'openai', 'gpt-3.5-turbo', 60),
('gpt-4', 'https://api.openai.com', 'your-openai-api-key', '/v1/chat/completions', '/v1/embeddings', 'openai', 'gpt-4', 120),
('local-llm', 'http://localhost:11434', 'ollama', '/v1/chat/completions', '/v1/embeddings', 'ollama', 'llama2', 30);

-- 3. 插入顾问配置
INSERT INTO ai_client_advisor (advisor_name, advisor_type, order_num, chat_memory_max_messages, rag_answer_top_k, rag_answer_filter_expression) VALUES 
('基础记忆顾问', 'ChatMemory', 1, 10, NULL, NULL),
('长期记忆顾问', 'ChatMemory', 1, 50, NULL, NULL),
('技术文档RAG顾问', 'RagAnswer', 2, NULL, 5, 'type == "technical"'),
('通用知识RAG顾问', 'RagAnswer', 2, NULL, 3, NULL);

-- 4. 插入MCP工具配置
INSERT INTO ai_client_tool_mcp (mcp_name, transport_type, sse_base_uri, sse_endpoint, stdio_config, request_timeout) VALUES 
('weather-tool', 'sse', 'http://localhost:8080', '/sse', NULL, 5),
('file-system-tool', 'stdio', NULL, NULL, '{"mcp-server-filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"]}}', 10),
('calculator-tool', 'sse', 'http://localhost:9999', '/sse?apikey=demo', NULL, 3);

-- 5. 插入AI客户端配置
INSERT INTO ai_client (system_prompt_id, model_bean_id) VALUES 
(1, 1),  -- 通用助手 + GPT-3.5
(2, 2),  -- 技术专家 + GPT-4
(3, 1),  -- 客服助手 + GPT-3.5
(1, 3);  -- 通用助手 + 本地模型

-- 6. 插入客户端与顾问关联关系
INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id) VALUES 
(1, 1),  -- 客户端1 使用 基础记忆顾问
(2, 2),  -- 客户端2 使用 长期记忆顾问
(2, 3),  -- 客户端2 使用 技术文档RAG顾问
(3, 1),  -- 客户端3 使用 基础记忆顾问
(4, 1),  -- 客户端4 使用 基础记忆顾问
(4, 4);  -- 客户端4 使用 通用知识RAG顾问

-- 7. 插入客户端与MCP工具关联关系
INSERT INTO ai_client_mcp_rel (client_id, mcp_bean_id) VALUES 
(1, 1),  -- 客户端1 使用 天气工具
(1, 3),  -- 客户端1 使用 计算器工具
(2, 2),  -- 客户端2 使用 文件系统工具
(3, 1),  -- 客户端3 使用 天气工具
(4, 1),  -- 客户端4 使用 天气工具
(4, 2);  -- 客户端4 使用 文件系统工具

-- 8. 插入模型工具配置关联
INSERT INTO ai_client_model_tool_config (model_id, tool_type, tool_id) VALUES 
(1, 'mcp', 1),  -- GPT-3.5 关联 天气工具
(1, 'mcp', 3),  -- GPT-3.5 关联 计算器工具
(2, 'mcp', 2),  -- GPT-4 关联 文件系统工具
(3, 'mcp', 1);  -- 本地模型 关联 天气工具
