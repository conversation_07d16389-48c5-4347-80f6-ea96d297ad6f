# AI Client 组装系统使用指南

## 概述

本系统基于Spring AI实现了一个灵活的AI客户端组装框架，支持动态配置和组装多种AI组件，包括：

- **Chat Models**: 支持OpenAI、Ollama等多种模型
- **Advisors**: 支持记忆管理、RAG问答等顾问
- **MCP Tools**: 支持SSE和STDIO两种传输方式的工具
- **System Prompts**: 可配置的系统提示词

## 系统架构

```
数据加载 → MCP工具组装 → 顾问组装 → 模型组装 → 客户端组装
   ↓           ↓           ↓         ↓          ↓
RootNode → McpNode → AdvisorNode → ModelNode → ClientNode
```

## 快速开始

### 1. 数据库初始化

```sql
-- 执行建表脚本
source docs/dev-ops/mysql/sql/ai_agent_tables.sql

-- 插入示例数据
source docs/dev-ops/mysql/sql/sample_data.sql
```

### 2. 配置文件

确保 `application-dev.yml` 中的配置正确：

```yaml
# 数据库配置
spring:
  datasource:
    url: *****************************************
    username: your_username
    password: your_password

# MyBatis配置
mybatis:
  mapper-locations: classpath:/mybatis/mapper/*.xml
  config-location: classpath:/mybatis/config/mybatis-config.xml
```

### 3. 启动应用

应用启动时会自动执行AI客户端组装流程。

## API接口

### 初始化AI客户端

```bash
POST /api/ai-client/initialize
```

### 重新组装指定客户端

```bash
POST /api/ai-client/reassemble/{clientId}
```

### 测试对话

```bash
POST /api/ai-client/chat/{clientId}
Content-Type: application/json

{
  "message": "你好，请介绍一下Spring Boot"
}
```

### 查看已注册的客户端

```bash
GET /api/ai-client/list
```

## 组件配置

### 1. 系统提示词 (System Prompts)

```sql
INSERT INTO ai_client_system_prompt (prompt_content) VALUES 
('你是一个专业的AI助手，请根据用户的问题提供准确、有帮助的回答。');
```

### 2. AI模型配置 (Models)

```sql
INSERT INTO ai_client_model (model_name, base_url, api_key, model_type, model_version) VALUES 
('gpt-3.5-turbo', 'https://api.openai.com', 'your-api-key', 'openai', 'gpt-3.5-turbo');
```

### 3. 顾问配置 (Advisors)

#### 记忆顾问 (ChatMemory)
```sql
INSERT INTO ai_client_advisor (advisor_name, advisor_type, chat_memory_max_messages) VALUES 
('基础记忆顾问', 'ChatMemory', 10);
```

#### RAG顾问 (RagAnswer)
```sql
INSERT INTO ai_client_advisor (advisor_name, advisor_type, rag_answer_top_k, rag_answer_filter_expression) VALUES 
('技术文档RAG顾问', 'RagAnswer', 5, 'type == "technical"');
```

### 4. MCP工具配置 (Tools)

#### SSE传输方式
```sql
INSERT INTO ai_client_tool_mcp (mcp_name, transport_type, sse_base_uri, sse_endpoint) VALUES 
('weather-tool', 'sse', 'http://localhost:8080', '/sse');
```

#### STDIO传输方式
```sql
INSERT INTO ai_client_tool_mcp (mcp_name, transport_type, stdio_config) VALUES 
('file-system-tool', 'stdio', '{"mcp-server-filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/tmp"]}}');
```

### 5. 客户端组装

```sql
-- 创建AI客户端
INSERT INTO ai_client (system_prompt_id, model_bean_id) VALUES (1, 1);

-- 关联顾问
INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id) VALUES (1, 1);

-- 关联MCP工具
INSERT INTO ai_client_mcp_rel (client_id, mcp_bean_id) VALUES (1, 1);
```

## 使用示例

### 示例1：基础对话客户端

- **模型**: GPT-3.5-turbo
- **顾问**: 基础记忆顾问 (10条消息)
- **工具**: 天气工具、计算器工具
- **提示词**: 通用AI助手

### 示例2：技术专家客户端

- **模型**: GPT-4
- **顾问**: 长期记忆顾问 (50条消息) + 技术文档RAG顾问
- **工具**: 文件系统工具
- **提示词**: 技术专家

### 示例3：客服助手客户端

- **模型**: GPT-3.5-turbo
- **顾问**: 基础记忆顾问
- **工具**: 天气工具
- **提示词**: 友好的客服助手

## 扩展开发

### 添加新的顾问类型

1. 在 `AiClientAdvisorNode.createAdvisor()` 方法中添加新的case
2. 实现对应的Spring AI Advisor
3. 更新数据库表结构（如需要）

### 添加新的传输方式

1. 在 `AiClientToolMcpNode.createMcpSyncClient()` 方法中添加新的case
2. 实现对应的MCP传输客户端
3. 更新配置表结构

### 添加新的模型类型

1. 在 `AiClientModelNode.createOpenAiChatModel()` 方法中添加支持
2. 或创建新的模型创建方法
3. 更新模型配置

## 注意事项

1. **VectorStore配置**: 使用RAG顾问需要配置VectorStore Bean
2. **API密钥**: 确保模型配置中的API密钥正确
3. **MCP服务**: 确保MCP工具服务正常运行
4. **内存管理**: 大量客户端可能消耗较多内存
5. **并发安全**: Bean注册过程是线程安全的

## 故障排除

### 常见问题

1. **VectorStore未配置**: 检查是否有VectorStore Bean配置
2. **MCP连接失败**: 检查MCP服务是否启动和网络连接
3. **API调用失败**: 检查API密钥和网络连接
4. **Bean注册失败**: 检查日志中的详细错误信息

### 日志查看

系统提供了详细的日志记录，可以通过日志查看组装过程和错误信息：

```
2025-08-03 10:00:00 INFO  - 开始执行AI客户端组装流程
2025-08-03 10:00:01 INFO  - === 第1阶段：数据加载 ===
2025-08-03 10:00:02 INFO  - 数据加载完成: clients=4, prompts=3, models=3, advisors=4, mcpTools=3
2025-08-03 10:00:03 INFO  - === 第2阶段：MCP工具组装 ===
2025-08-03 10:00:04 INFO  - 成功注册MCP工具客户端: AiClientToolMcp_1 -> weather-tool
```

## 性能优化

1. **数据库连接池**: 配置合适的数据库连接池大小
2. **线程池**: 调整线程池配置以适应并发需求
3. **缓存**: 考虑添加配置缓存以减少数据库查询
4. **懒加载**: 对于不常用的组件可以考虑懒加载策略
