package cn.iflytek.config;

import cn.iflytek.domain.agent.service.factory.DefaultArmoryStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * AI客户端初始化器
 * 应用启动时自动初始化AI客户端
 */
@Slf4j
@Component
public class AiClientInitializer implements ApplicationRunner {

    @Resource
    private DefaultArmoryStrategyFactory armoryStrategyFactory;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("开始自动初始化AI客户端...");
        
        try {
            armoryStrategyFactory.assembleAiClients();
            log.info("AI客户端自动初始化完成");
        } catch (Exception e) {
            log.error("AI客户端自动初始化失败，请检查配置", e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
