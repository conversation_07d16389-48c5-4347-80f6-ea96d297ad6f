package cn.iflytek.controller;

import cn.iflytek.domain.agent.model.AiClientAdvisor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.QuestionAnswerAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 顾问测试控制器
 * 用于测试各种Advisor的创建和配置
 */
@Slf4j
@RestController
@RequestMapping("/api/advisor-test")
public class AdvisorTestController {

    @Resource(required = false)
    private VectorStore vectorStore;

    /**
     * 测试ChatMemory顾问创建
     */
    @PostMapping("/test-chat-memory")
    public Map<String, Object> testChatMemoryAdvisor(@RequestParam(defaultValue = "10") int maxMessages) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 创建ChatMemory配置
            AiClientAdvisor.ChatMemory chatMemory = AiClientAdvisor.ChatMemory.builder()
                    .maxMessages(maxMessages)
                    .build();

            // 创建PromptChatMemoryAdvisor
            Advisor advisor = PromptChatMemoryAdvisor.builder(MessageWindowChatMemory.builder()
                    .maxMessages(chatMemory.getMaxMessages())
                    .build())
                    .build();

            result.put("success", true);
            result.put("advisorType", "ChatMemory");
            result.put("advisorClass", advisor.getClass().getSimpleName());
            result.put("maxMessages", maxMessages);
            result.put("message", "ChatMemory顾问创建成功");
            
            log.info("ChatMemory顾问创建成功: maxMessages={}", maxMessages);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "ChatMemory顾问创建失败: " + e.getMessage());
            log.error("ChatMemory顾问创建失败", e);
        }
        
        return result;
    }

    /**
     * 测试RAG顾问创建
     */
    @PostMapping("/test-rag-advisor")
    public Map<String, Object> testRagAdvisor(
            @RequestParam(defaultValue = "5") int topK,
            @RequestParam(required = false) String filterExpression) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            if (vectorStore == null) {
                result.put("success", false);
                result.put("message", "VectorStore未配置，无法创建RAG顾问");
                return result;
            }

            // 创建RagAnswer配置
            AiClientAdvisor.RagAnswer ragAnswer = AiClientAdvisor.RagAnswer.builder()
                    .topK(topK)
                    .filterExpression(filterExpression)
                    .build();

            // 创建QuestionAnswerAdvisor
            Advisor advisor = QuestionAnswerAdvisor.builder(vectorStore)
                    .searchRequest(SearchRequest.builder()
                            .topK(ragAnswer.getTopK())
                            .filterExpression(ragAnswer.getFilterExpression())
                            .build())
                    .build();

            result.put("success", true);
            result.put("advisorType", "RagAnswer");
            result.put("advisorClass", advisor.getClass().getSimpleName());
            result.put("topK", topK);
            result.put("filterExpression", filterExpression);
            result.put("message", "RAG顾问创建成功");
            
            log.info("RAG顾问创建成功: topK={}, filterExpression={}", topK, filterExpression);
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "RAG顾问创建失败: " + e.getMessage());
            log.error("RAG顾问创建失败", e);
        }
        
        return result;
    }

    /**
     * 测试完整的顾问配置
     */
    @PostMapping("/test-full-advisor")
    public Map<String, Object> testFullAdvisor(@RequestBody AiClientAdvisor advisorConfig) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            Advisor advisor = createAdvisor(advisorConfig);
            
            result.put("success", true);
            result.put("advisorId", advisorConfig.getId());
            result.put("advisorName", advisorConfig.getAdvisorName());
            result.put("advisorType", advisorConfig.getAdvisorType());
            result.put("advisorClass", advisor.getClass().getSimpleName());
            result.put("message", "顾问创建成功");
            
            log.info("顾问创建成功: name={}, type={}", advisorConfig.getAdvisorName(), advisorConfig.getAdvisorType());
            
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "顾问创建失败: " + e.getMessage());
            log.error("顾问创建失败: advisorName={}, advisorType={}", 
                advisorConfig.getAdvisorName(), advisorConfig.getAdvisorType(), e);
        }
        
        return result;
    }

    /**
     * 获取VectorStore状态
     */
    @GetMapping("/vector-store-status")
    public Map<String, Object> getVectorStoreStatus() {
        Map<String, Object> result = new HashMap<>();
        
        if (vectorStore == null) {
            result.put("configured", false);
            result.put("message", "VectorStore未配置");
        } else {
            result.put("configured", true);
            result.put("vectorStoreClass", vectorStore.getClass().getSimpleName());
            result.put("message", "VectorStore已配置");
        }
        
        return result;
    }

    /**
     * 创建顾问的核心逻辑（与AiClientAdvisorNode中的逻辑一致）
     */
    private Advisor createAdvisor(AiClientAdvisor aiClientAdvisorVO) {
        String advisorType = aiClientAdvisorVO.getAdvisorType();
        switch (advisorType) {
            case "ChatMemory" -> {
                AiClientAdvisor.ChatMemory chatMemory = aiClientAdvisorVO.getChatMemory();
                return PromptChatMemoryAdvisor.builder(MessageWindowChatMemory.builder()
                        .maxMessages(chatMemory.getMaxMessages())
                        .build())
                        .build();
            }
            case "RagAnswer" -> {
                if (vectorStore == null) {
                    log.warn("VectorStore未配置，跳过RAG顾问创建: {}", aiClientAdvisorVO.getAdvisorName());
                    throw new RuntimeException("VectorStore未配置，无法创建RAG顾问");
                }
                AiClientAdvisor.RagAnswer ragAnswer = aiClientAdvisorVO.getRagAnswer();
                return QuestionAnswerAdvisor.builder(vectorStore)
                        .searchRequest(SearchRequest.builder()
                                .topK(ragAnswer.getTopK())
                                .filterExpression(ragAnswer.getFilterExpression())
                                .build())
                        .build();
            }
        }

        throw new RuntimeException("err! advisorType " + advisorType + " not exist!");
    }
}
