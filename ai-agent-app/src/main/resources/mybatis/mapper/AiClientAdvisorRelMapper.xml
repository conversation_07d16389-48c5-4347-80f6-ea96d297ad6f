<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientAdvisorRelPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="client_id" property="clientId" jdbcType="BIGINT"/>
        <result column="advisor_bean_id" property="advisorBeanId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, client_id, advisor_bean_id, create_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientAdvisorRelPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id, create_time)
        VALUES (#{clientId}, #{advisorBeanId}, NOW())
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_client_advisor_rel (client_id, advisor_bean_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.clientId}, #{item.advisorBeanId}, NOW())
        </foreach>
    </insert>

    <select id="selectByClientId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor_rel
        WHERE client_id = #{clientId}
        ORDER BY id DESC
    </select>

    <select id="selectByAdvisorBeanId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor_rel
        WHERE advisor_bean_id = #{advisorBeanId}
        ORDER BY id DESC
    </select>

    <select id="selectByClientIdAndAdvisorBeanId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor_rel
        WHERE client_id = #{clientId} AND advisor_bean_id = #{advisorBeanId}
    </select>

    <delete id="deleteByClientId" parameterType="java.lang.Long">
        DELETE FROM ai_client_advisor_rel WHERE client_id = #{clientId}
    </delete>

    <delete id="deleteByAdvisorBeanId" parameterType="java.lang.Long">
        DELETE FROM ai_client_advisor_rel WHERE advisor_bean_id = #{advisorBeanId}
    </delete>

    <delete id="deleteByClientIdAndAdvisorBeanId">
        DELETE FROM ai_client_advisor_rel 
        WHERE client_id = #{clientId} AND advisor_bean_id = #{advisorBeanId}
    </delete>

</mapper>
