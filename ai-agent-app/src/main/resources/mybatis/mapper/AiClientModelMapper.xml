<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientModelMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientModelPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="model_name" property="modelName" jdbcType="VARCHAR"/>
        <result column="base_url" property="baseUrl" jdbcType="VARCHAR"/>
        <result column="api_key" property="apiKey" jdbcType="VARCHAR"/>
        <result column="completions_path" property="completionsPath" jdbcType="VARCHAR"/>
        <result column="embeddings_path" property="embeddingsPath" jdbcType="VARCHAR"/>
        <result column="model_type" property="modelType" jdbcType="VARCHAR"/>
        <result column="model_version" property="modelVersion" jdbcType="VARCHAR"/>
        <result column="timeout" property="timeout" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, model_name, base_url, api_key, completions_path, embeddings_path, 
        model_type, model_version, timeout, create_time, update_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientModelPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_model (model_name, base_url, api_key, completions_path, embeddings_path, 
                                     model_type, model_version, timeout, create_time, update_time)
        VALUES (#{modelName}, #{baseUrl}, #{apiKey}, #{completionsPath}, #{embeddingsPath}, 
                #{modelType}, #{modelVersion}, #{timeout}, NOW(), NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model
        WHERE id = #{id}
    </select>

    <select id="selectByModelName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model
        WHERE model_name = #{modelName}
    </select>

    <select id="selectByModelType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model
        WHERE model_type = #{modelType}
        ORDER BY id DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model
        ORDER BY id DESC
    </select>

    <update id="updateById" parameterType="cn.iflytek.infrastructure.dao.po.AiClientModelPO">
        UPDATE ai_client_model
        SET model_name = #{modelName},
            base_url = #{baseUrl},
            api_key = #{apiKey},
            completions_path = #{completionsPath},
            embeddings_path = #{embeddingsPath},
            model_type = #{modelType},
            model_version = #{modelVersion},
            timeout = #{timeout},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_model WHERE id = #{id}
    </delete>

</mapper>
