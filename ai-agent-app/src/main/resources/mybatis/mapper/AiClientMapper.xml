<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientPO">
        <id column="client_id" property="clientId" jdbcType="BIGINT"/>
        <result column="system_prompt_id" property="systemPromptId" jdbcType="BIGINT"/>
        <result column="model_bean_id" property="modelBeanId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        client_id, system_prompt_id, model_bean_id, create_time, update_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientPO" useGeneratedKeys="true" keyProperty="clientId">
        INSERT INTO ai_client (system_prompt_id, model_bean_id, create_time, update_time)
        VALUES (#{systemPromptId}, #{modelBeanId}, NOW(), NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client
        WHERE client_id = #{clientId}
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client
        ORDER BY client_id DESC
    </select>

    <select id="selectByModelBeanId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client
        WHERE model_bean_id = #{modelBeanId}
        ORDER BY client_id DESC
    </select>

    <select id="selectBySystemPromptId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client
        WHERE system_prompt_id = #{systemPromptId}
        ORDER BY client_id DESC
    </select>

    <update id="updateById" parameterType="cn.iflytek.infrastructure.dao.po.AiClientPO">
        UPDATE ai_client
        SET system_prompt_id = #{systemPromptId},
            model_bean_id = #{modelBeanId},
            update_time = NOW()
        WHERE client_id = #{clientId}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client WHERE client_id = #{clientId}
    </delete>

</mapper>
