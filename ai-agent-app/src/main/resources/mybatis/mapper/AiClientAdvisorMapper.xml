<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientAdvisorMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="advisor_name" property="advisorName" jdbcType="VARCHAR"/>
        <result column="advisor_type" property="advisorType" jdbcType="VARCHAR"/>
        <result column="order_num" property="orderNum" jdbcType="INTEGER"/>
        <result column="chat_memory_max_messages" property="chatMemoryMaxMessages" jdbcType="INTEGER"/>
        <result column="rag_answer_top_k" property="ragAnswerTopK" jdbcType="INTEGER"/>
        <result column="rag_answer_filter_expression" property="ragAnswerFilterExpression" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, advisor_name, advisor_type, order_num, chat_memory_max_messages, 
        rag_answer_top_k, rag_answer_filter_expression, create_time, update_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_advisor (advisor_name, advisor_type, order_num, chat_memory_max_messages, 
                                       rag_answer_top_k, rag_answer_filter_expression, create_time, update_time)
        VALUES (#{advisorName}, #{advisorType}, #{orderNum}, #{chatMemoryMaxMessages}, 
                #{ragAnswerTopK}, #{ragAnswerFilterExpression}, NOW(), NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE id = #{id}
    </select>

    <select id="selectByAdvisorName" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE advisor_name = #{advisorName}
    </select>

    <select id="selectByAdvisorType" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        WHERE advisor_type = #{advisorType}
        ORDER BY order_num ASC, id DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_advisor
        ORDER BY order_num ASC, id DESC
    </select>

    <update id="updateById" parameterType="cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO">
        UPDATE ai_client_advisor
        SET advisor_name = #{advisorName},
            advisor_type = #{advisorType},
            order_num = #{orderNum},
            chat_memory_max_messages = #{chatMemoryMaxMessages},
            rag_answer_top_k = #{ragAnswerTopK},
            rag_answer_filter_expression = #{ragAnswerFilterExpression},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_advisor WHERE id = #{id}
    </delete>

</mapper>
