<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientModelToolConfigPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="model_id" property="modelId" jdbcType="BIGINT"/>
        <result column="tool_type" property="toolType" jdbcType="VARCHAR"/>
        <result column="tool_id" property="toolId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, model_id, tool_type, tool_id, create_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientModelToolConfigPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_model_tool_config (model_id, tool_type, tool_id, create_time)
        VALUES (#{modelId}, #{toolType}, #{toolId}, NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model_tool_config
        WHERE id = #{id}
    </select>

    <select id="selectByModelId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model_tool_config
        WHERE model_id = #{modelId}
        ORDER BY id DESC
    </select>

    <select id="selectByToolTypeAndId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model_tool_config
        WHERE tool_type = #{toolType} AND tool_id = #{toolId}
        ORDER BY id DESC
    </select>

    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_model_tool_config
        ORDER BY id DESC
    </select>

    <update id="updateById" parameterType="cn.iflytek.infrastructure.dao.po.AiClientModelToolConfigPO">
        UPDATE ai_client_model_tool_config
        SET model_id = #{modelId},
            tool_type = #{toolType},
            tool_id = #{toolId}
        WHERE id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM ai_client_model_tool_config WHERE id = #{id}
    </delete>

    <delete id="deleteByModelId" parameterType="java.lang.Long">
        DELETE FROM ai_client_model_tool_config WHERE model_id = #{modelId}
    </delete>

</mapper>
