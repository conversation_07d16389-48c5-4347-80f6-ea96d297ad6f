<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.iflytek.infrastructure.dao.AiClientMcpRelMapper">

    <resultMap id="BaseResultMap" type="cn.iflytek.infrastructure.dao.po.AiClientMcpRelPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="client_id" property="clientId" jdbcType="BIGINT"/>
        <result column="mcp_bean_id" property="mcpBeanId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, client_id, mcp_bean_id, create_time
    </sql>

    <insert id="insert" parameterType="cn.iflytek.infrastructure.dao.po.AiClientMcpRelPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO ai_client_mcp_rel (client_id, mcp_bean_id, create_time)
        VALUES (#{clientId}, #{mcpBeanId}, NOW())
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO ai_client_mcp_rel (client_id, mcp_bean_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.clientId}, #{item.mcpBeanId}, NOW())
        </foreach>
    </insert>

    <select id="selectByClientId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_mcp_rel
        WHERE client_id = #{clientId}
        ORDER BY id DESC
    </select>

    <select id="selectByMcpBeanId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_mcp_rel
        WHERE mcp_bean_id = #{mcpBeanId}
        ORDER BY id DESC
    </select>

    <select id="selectByClientIdAndMcpBeanId" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM ai_client_mcp_rel
        WHERE client_id = #{clientId} AND mcp_bean_id = #{mcpBeanId}
    </select>

    <delete id="deleteByClientId" parameterType="java.lang.Long">
        DELETE FROM ai_client_mcp_rel WHERE client_id = #{clientId}
    </delete>

    <delete id="deleteByMcpBeanId" parameterType="java.lang.Long">
        DELETE FROM ai_client_mcp_rel WHERE mcp_bean_id = #{mcpBeanId}
    </delete>

    <delete id="deleteByClientIdAndMcpBeanId">
        DELETE FROM ai_client_mcp_rel 
        WHERE client_id = #{clientId} AND mcp_bean_id = #{mcpBeanId}
    </delete>

</mapper>
