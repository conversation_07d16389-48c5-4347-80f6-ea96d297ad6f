package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.adapter.repository.*;
import cn.iflytek.domain.agent.model.*;
import cn.iflytek.domain.agent.service.dto.ArmoryDataContext;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class RootNode extends AbstractArmorySupport {

    @Resource
    private AiClientRepository aiClientRepository;

    @Resource
    private AiClientSystemPromptRepository systemPromptRepository;

    @Resource
    private AiClientModelRepository modelRepository;

    @Resource
    private AiClientAdvisorRepository advisorRepository;

    @Resource
    private AiClientToolMcpRepository mcpToolRepository;

    public ArmoryDataContext doApply() {
        try {
            return multiThreadLoadData();
        } catch (Exception e) {
            log.error("数据加载失败", e);
            throw new RuntimeException("数据加载失败", e);
        }
    }

    private ArmoryDataContext multiThreadLoadData() throws ExecutionException, InterruptedException, TimeoutException {
        // 并行加载各种数据
        CompletableFuture<List<AiClient>> clientsFuture = CompletableFuture.supplyAsync(
            () -> aiClientRepository.findAll(), threadPoolExecutor);

        CompletableFuture<Map<Long, AiClientSystemPrompt>> promptsFuture = CompletableFuture.supplyAsync(
            () -> systemPromptRepository.findAll().stream()
                .collect(Collectors.toMap(AiClientSystemPrompt::getId, Function.identity())),
            threadPoolExecutor);

        CompletableFuture<Map<Long, AiClientModel>> modelsFuture = CompletableFuture.supplyAsync(
            () -> modelRepository.findAll().stream()
                .collect(Collectors.toMap(AiClientModel::getId, Function.identity())),
            threadPoolExecutor);

        CompletableFuture<Map<Long, AiClientAdvisor>> advisorsFuture = CompletableFuture.supplyAsync(
            () -> advisorRepository.findAll().stream()
                .collect(Collectors.toMap(AiClientAdvisor::getId, Function.identity())),
            threadPoolExecutor);

        CompletableFuture<Map<Long, AiClientToolMcp>> mcpToolsFuture = CompletableFuture.supplyAsync(
            () -> mcpToolRepository.findAll().stream()
                .collect(Collectors.toMap(AiClientToolMcp::getId, Function.identity())),
            threadPoolExecutor);

        // 等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
            clientsFuture, promptsFuture, modelsFuture, advisorsFuture, mcpToolsFuture);

        allFutures.get(30, TimeUnit.SECONDS);

        // 构建数据上下文
        ArmoryDataContext context = ArmoryDataContext.builder()
            .aiClientList(clientsFuture.get())
            .systemPromptMap(promptsFuture.get())
            .modelMap(modelsFuture.get())
            .advisorMap(advisorsFuture.get())
            .mcpToolMap(mcpToolsFuture.get())
            .build();

        log.info("数据加载完成: clients={}, prompts={}, models={}, advisors={}, mcpTools={}",
            context.getAiClientList().size(),
            context.getSystemPromptMap().size(),
            context.getModelMap().size(),
            context.getAdvisorMap().size(),
            context.getMcpToolMap().size());

        return context;
    }
}
