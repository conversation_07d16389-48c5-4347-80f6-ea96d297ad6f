package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.model.AiClientAdvisor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class AiClientAdvisorNode extends AbstractArmorySupport{

    @Resource
    private VectorStore vectorStore;

    public void doApply(Map<Long, AiClientAdvisor> advisorMap) {
        log.info("开始构建顾问，数量: {}", advisorMap.size());

        for (Map.Entry<Long, AiClientAdvisor> entry : advisorMap.entrySet()) {
            Long advisorId = entry.getKey();
            AiClientAdvisor advisor = entry.getValue();

            try {
                Advisor advisorInstance = createAdvisor(advisor);
                String beanName = beanName(advisorId);
                registerBean(beanName, Advisor.class, advisorInstance);
                log.info("成功注册顾问: {} -> {} ({})", beanName, advisor.getAdvisorName(), advisor.getAdvisorType());
            } catch (Exception e) {
                log.error("构建顾问失败: advisorId={}, advisorName={}, advisorType={}",
                    advisorId, advisor.getAdvisorName(), advisor.getAdvisorType(), e);
                throw new RuntimeException("构建顾问失败: " + advisor.getAdvisorName(), e);
            }
        }

        log.info("顾问构建完成");
    }

    @Override
    protected String beanName(Long id) {
        return "AiClientAdvisor_" + id;
    }

    private Advisor createAdvisor(AiClientAdvisor aiClientAdvisorVO) {
        String advisorType = aiClientAdvisorVO.getAdvisorType();
        switch (advisorType) {
            case "ChatMemory" -> {
                AiClientAdvisor.ChatMemory chatMemory = aiClientAdvisorVO.getChatMemory();
//                return new PromptChatMemoryAdvisor(new InMemoryChatMemory());
                return new PromptChatMemoryAdvisor(MessageWindowChatMemory.builder()
                        .maxMessages(chatMemory.getMaxMessages())
                        .build());
            }
            case "RagAnswer" -> {
                if (vectorStore == null) {
                    log.warn("VectorStore未配置，跳过RAG顾问创建: {}", aiClientAdvisorVO.getAdvisorName());
                    throw new RuntimeException("VectorStore未配置，无法创建RAG顾问");
                }
                AiClientAdvisor.RagAnswer ragAnswer = aiClientAdvisorVO.getRagAnswer();
                return QuestionAnswerAdvisor.builder(vectorStore)
                        .searchRequest(SearchRequest.builder()
                                .topK(ragAnswer.getTopK())
                                .filterExpression(ragAnswer.getFilterExpression())
                                .build())
                        .build();
            }
        }

        throw new RuntimeException("err! advisorType " + advisorType + " not exist!");
    }
}
