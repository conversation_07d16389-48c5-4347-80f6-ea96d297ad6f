package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.model.AiClientAdvisor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AiClientAdvisorNode extends AbstractArmorySupport{
    public String doApply()  {
        //执行构建，创建构建顾问访问对象，使用父类的通用注册方法注册
        return null;
    }

    @Override
    protected String beanName(Long id) {
        return "AiClientAdvisor_" + id;
    }

    private Advisor createAdvisor(AiClientAdvisor aiClientAdvisorVO) {
        String advisorType = aiClientAdvisorVO.getAdvisorType();
        switch (advisorType) {
            case "ChatMemory" -> {
                AiClientAdvisor.ChatMemory chatMemory = aiClientAdvisorVO.getChatMemory();
//                return new PromptChatMemoryAdvisor(new InMemoryChatMemory());
                return new PromptChatMemoryAdvisor(MessageWindowChatMemory.builder()
                        .maxMessages(chatMemory.getMaxMessages())
                        .build());
            }
            case "RagAnswer" -> {
                AiClientAdvisor.RagAnswer ragAnswer = aiClientAdvisorVO.getRagAnswer();
                //todo rag修复
                return new RagAnswerAdvisor(vectorStore, SearchRequest.builder()
                        .topK(ragAnswer.getTopK())
                        .filterExpression(ragAnswer.getFilterExpression())
                        .build());
            }
        }

        throw new RuntimeException("err! advisorType " + advisorType + " not exist!");
    }
}
