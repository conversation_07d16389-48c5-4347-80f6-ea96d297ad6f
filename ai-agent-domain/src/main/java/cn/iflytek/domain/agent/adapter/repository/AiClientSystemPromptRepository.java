package cn.iflytek.domain.agent.adapter.repository;

import cn.iflytek.domain.agent.model.AiClientSystemPrompt;

import java.util.List;

/**
 * AI客户端系统提示词仓储接口
 */
public interface AiClientSystemPromptRepository {

    /**
     * 保存系统提示词
     */
    Long save(AiClientSystemPrompt prompt);

    /**
     * 根据ID查询系统提示词
     */
    AiClientSystemPrompt findById(Long id);

    /**
     * 查询所有系统提示词
     */
    List<AiClientSystemPrompt> findAll();

    /**
     * 更新系统提示词
     */
    boolean update(AiClientSystemPrompt prompt);

    /**
     * 根据ID删除系统提示词
     */
    boolean deleteById(Long id);
}
