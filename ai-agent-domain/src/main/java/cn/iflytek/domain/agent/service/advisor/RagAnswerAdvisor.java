package cn.iflytek.domain.agent.service.advisor;

import org.springframework.ai.chat.client.advisor.api.AdvisedRequest;
import org.springframework.ai.chat.client.advisor.api.AdvisedResponse;
import org.springframework.ai.chat.client.advisor.api.CallAroundAdvisor;
import org.springframework.ai.chat.client.advisor.api.CallAroundAdvisorChain;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * RAG答案顾问实现
 */
public class RagAnswerAdvisor implements CallAroundAdvisor {

    private final VectorStore vectorStore;
    private final SearchRequest searchRequest;

    public RagAnswerAdvisor(VectorStore vectorStore, SearchRequest searchRequest) {
        this.vectorStore = vectorStore;
        this.searchRequest = searchRequest;
    }

    @Override
    public AdvisedResponse aroundCall(AdvisedRequest advisedRequest, CallAroundAdvisorChain chain) {
        // 获取用户问题
        String userMessage = advisedRequest.userText();
        
        if (!StringUtils.hasText(userMessage)) {
            return chain.nextAroundCall(advisedRequest);
        }

        // 从向量数据库搜索相关文档
        List<Document> documents = vectorStore.similaritySearch(
            SearchRequest.builder()
                .query(userMessage)
                .topK(searchRequest.getTopK())
                .filterExpression(searchRequest.getFilterExpression())
                .build()
        );

        if (documents.isEmpty()) {
            return chain.nextAroundCall(advisedRequest);
        }

        // 构建上下文信息
        String context = documents.stream()
            .map(Document::getContent)
            .collect(Collectors.joining("\n\n"));

        // 构建增强的系统消息
        String enhancedSystemMessage = buildEnhancedSystemMessage(context);

        // 创建新的请求，添加RAG上下文
        AdvisedRequest newRequest = AdvisedRequest.from(advisedRequest)
            .withSystemText(enhancedSystemMessage)
            .build();

        return chain.nextAroundCall(newRequest);
    }

    private String buildEnhancedSystemMessage(String context) {
        return String.format(
            "请基于以下上下文信息回答用户的问题。如果上下文信息不足以回答问题，请明确说明。\n\n" +
            "上下文信息：\n%s\n\n" +
            "请确保回答准确、相关且有帮助。",
            context
        );
    }

    @Override
    public String getName() {
        return "RagAnswerAdvisor";
    }

    @Override
    public int getOrder() {
        return 0;
    }
}
