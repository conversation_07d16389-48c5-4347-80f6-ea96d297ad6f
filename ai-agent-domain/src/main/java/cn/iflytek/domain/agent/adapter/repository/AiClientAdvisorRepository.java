package cn.iflytek.domain.agent.adapter.repository;

import cn.iflytek.domain.agent.model.AiClientAdvisor;

import java.util.List;

/**
 * AI客户端顾问配置仓储接口
 */
public interface AiClientAdvisorRepository {

    /**
     * 保存顾问配置
     */
    Long save(AiClientAdvisor advisor);

    /**
     * 根据ID查询顾问配置
     */
    AiClientAdvisor findById(Long id);

    /**
     * 根据顾问名称查询顾问配置
     */
    AiClientAdvisor findByAdvisorName(String advisorName);

    /**
     * 根据顾问类型查询顾问配置列表
     */
    List<AiClientAdvisor> findByAdvisorType(String advisorType);

    /**
     * 查询所有顾问配置
     */
    List<AiClientAdvisor> findAll();

    /**
     * 更新顾问配置
     */
    boolean update(AiClientAdvisor advisor);

    /**
     * 根据ID删除顾问配置
     */
    boolean deleteById(Long id);
}
