package cn.iflytek.domain.agent.adapter.repository;

import cn.iflytek.domain.agent.model.AiClient;

import java.util.List;

/**
 * AI客户端仓储接口
 */
public interface AiClientRepository {

    /**
     * 保存AI客户端
     */
    Long save(AiClient aiClient);

    /**
     * 根据客户端ID查询AI客户端
     */
    AiClient findByClientId(Long clientId);

    /**
     * 查询所有AI客户端
     */
    List<AiClient> findAll();

    /**
     * 根据模型Bean ID查询AI客户端列表
     */
    List<AiClient> findByModelBeanId(Long modelBeanId);

    /**
     * 根据系统提示词ID查询AI客户端列表
     */
    List<AiClient> findBySystemPromptId(Long systemPromptId);

    /**
     * 更新AI客户端
     */
    boolean update(AiClient aiClient);

    /**
     * 根据客户端ID删除AI客户端
     */
    boolean deleteByClientId(Long clientId);
}
