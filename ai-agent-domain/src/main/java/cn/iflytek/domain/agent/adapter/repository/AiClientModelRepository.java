package cn.iflytek.domain.agent.adapter.repository;

import cn.iflytek.domain.agent.model.AiClientModel;

import java.util.List;

/**
 * AI客户端模型配置仓储接口
 */
public interface AiClientModelRepository {

    /**
     * 保存模型配置
     */
    Long save(AiClientModel model);

    /**
     * 根据ID查询模型配置
     */
    AiClientModel findById(Long id);

    /**
     * 根据模型名称查询模型配置
     */
    AiClientModel findByModelName(String modelName);

    /**
     * 根据模型类型查询模型配置列表
     */
    List<AiClientModel> findByModelType(String modelType);

    /**
     * 查询所有模型配置
     */
    List<AiClientModel> findAll();

    /**
     * 更新模型配置
     */
    boolean update(AiClientModel model);

    /**
     * 根据ID删除模型配置
     */
    boolean deleteById(Long id);
}
