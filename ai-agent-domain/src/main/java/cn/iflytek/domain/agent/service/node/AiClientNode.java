package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.model.AiClient;
import cn.iflytek.domain.agent.model.AiClientSystemPrompt;
import cn.iflytek.domain.agent.service.dto.ArmoryDataContext;
import com.alibaba.fastjson.JSON;
import io.modelcontextprotocol.client.McpSyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AiClientNode extends AbstractArmorySupport{

    @Override
    protected String beanName(Long id) {
        return "ChatClient_" + id;
    }
    public void doApply(ArmoryDataContext dataContext) {
        List<AiClient> aiClientList = dataContext.getAiClientList();
        Map<Long, AiClientSystemPrompt> systemPromptMap = dataContext.getSystemPromptMap();

        log.info("开始构建AI客户端，数量: {}", aiClientList.size());

        for (AiClient aiClient : aiClientList) {
            try {
                // 1. 预设话术
                String defaultSystem = "AI 智能体";
                AiClientSystemPrompt systemPrompt = systemPromptMap.get(aiClient.getSystemPromptId());
                if (systemPrompt != null) {
                    defaultSystem = systemPrompt.getPromptContent();
                }

                // 2. chatModel
                OpenAiChatModel chatModel = getBean(aiClient.getModelBeanName());

                // 3. ToolCallbackProvider
                List<McpSyncClient> mcpSyncClients = new ArrayList<>();
                List<String> mcpBeanNameList = aiClient.getMcpBeanNameList();
                if (mcpBeanNameList != null && !mcpBeanNameList.isEmpty()) {
                    for (String mcpBeanName : mcpBeanNameList) {
                        try {
                            McpSyncClient mcpClient = getBean(mcpBeanName);
                            mcpSyncClients.add(mcpClient);
                        } catch (Exception e) {
                            log.warn("获取MCP客户端失败: {}, 跳过", mcpBeanName, e);
                        }
                    }
                }

                // 4. Advisor
                List<Advisor> advisors = new ArrayList<>();
                List<String> advisorBeanNameList = aiClient.getAdvisorBeanNameList();
                if (advisorBeanNameList != null && !advisorBeanNameList.isEmpty()) {
                    for (String advisorBeanName : advisorBeanNameList) {
                        try {
                            Advisor advisor = getBean(advisorBeanName);
                            advisors.add(advisor);
                        } catch (Exception e) {
                            log.warn("获取顾问失败: {}, 跳过", advisorBeanName, e);
                        }
                    }
                }

                // 5. 构建对话客户端
                ChatClient.Builder builder = ChatClient.builder(chatModel)
                        .defaultSystem(defaultSystem);

                if (!mcpSyncClients.isEmpty()) {
                    builder.defaultToolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClients.toArray(new McpSyncClient[0])));
                }

                if (!advisors.isEmpty()) {
                    builder.defaultAdvisors(advisors.toArray(new Advisor[0]));
                }

                ChatClient chatClient = builder.build();

                String beanName = beanName(aiClient.getClientId());
                registerBean(beanName, ChatClient.class, chatClient);
                log.info("成功注册AI客户端: {} -> clientId={}", beanName, aiClient.getClientId());

            } catch (Exception e) {
                log.error("构建AI客户端失败: clientId={}", aiClient.getClientId(), e);
                throw new RuntimeException("构建AI客户端失败: clientId=" + aiClient.getClientId(), e);
            }
        }

        log.info("AI客户端构建完成");
    }
}
