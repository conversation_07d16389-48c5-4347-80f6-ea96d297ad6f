package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.model.AiClient;
import cn.iflytek.domain.agent.model.AiClientSystemPrompt;
import com.alibaba.fastjson.JSON;
import io.modelcontextprotocol.client.McpSyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.api.Advisor;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class AiClientNode extends AbstractArmorySupport{

    @Override
    protected String beanName(Long id) {
        return "ChatClient_" + id;
    }
    public String doApply()  {

        for (AiClient aiClientVO : aiClientVOList) {
            // 1. 预设话术
            String defaultSystem = "AI 智能体";
            AiClientSystemPrompt systemPrompt = aiClientSystemPromptVOMap.get(aiClientVO.getSystemPromptId());
            if (null != systemPrompt) {
                defaultSystem = systemPrompt.getPromptContent();
            }

            // 2. chatModel
            OpenAiChatModel chatModel = getBean(aiClientVO.getModelBeanName());

            // 3. ToolCallbackProvider
            List<McpSyncClient> mcpSyncClients = new ArrayList<>();
            List<String> mcpBeanNameList = aiClientVO.getMcpBeanNameList();
            for (String mcpBeanName : mcpBeanNameList) {
                mcpSyncClients.add(getBean(mcpBeanName));
            }

            // 4. Advisor
            List<Advisor> advisors = new ArrayList<>();
            List<String> advisorBeanNameList = aiClientVO.getAdvisorBeanNameList();
            for (String advisorBeanName : advisorBeanNameList) {
                advisors.add(getBean(advisorBeanName));
            }

            Advisor[] advisorArray = advisors.toArray(new Advisor[]{});

            // 5. 构建对话客户端
            ChatClient chatClient = ChatClient.builder(chatModel)
                    .defaultSystem(defaultSystem)
                    .defaultToolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClients.toArray(new McpSyncClient[]{})))
                    .defaultAdvisors(advisorArray)
                    .build();

            registerBean(beanName(aiClientVO.getClientId()), ChatClient.class, chatClient);
        }
        return null;

    }
}
