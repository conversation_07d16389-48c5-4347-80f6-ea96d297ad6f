package cn.iflytek.domain.agent.service.factory;

import cn.iflytek.domain.agent.service.dto.ArmoryDataContext;
import cn.iflytek.domain.agent.service.node.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;

/**
 * 默认军械库策略工厂
 * 负责编排AI客户端组装流程
 */
@Slf4j
@Service
public class DefaultArmoryStrategyFactory {

    @Resource
    private RootNode rootNode;

    @Resource
    private AiClientToolMcpNode mcpNode;

    @Resource
    private AiClientAdvisorNode advisorNode;

    @Resource
    private AiClientModelNode modelNode;

    @Resource
    private AiClientNode clientNode;

    /**
     * 执行AI客户端组装流程
     * 流程：数据加载 -> MCP工具组装 -> 顾问组装 -> 模型组装 -> 客户端组装
     */
    public void assembleAiClients() {
        log.info("开始执行AI客户端组装流程");

        try {
            // 1. 数据加载阶段
            log.info("=== 第1阶段：数据加载 ===");
            ArmoryDataContext dataContext = rootNode.doApply();

            // 2. MCP工具组装阶段
            log.info("=== 第2阶段：MCP工具组装 ===");
            mcpNode.doApply(dataContext.getMcpToolMap());

            // 3. 顾问组装阶段
            log.info("=== 第3阶段：顾问组装 ===");
            advisorNode.doApply(dataContext.getAdvisorMap());

            // 4. 模型组装阶段
            log.info("=== 第4阶段：模型组装 ===");
            modelNode.doApply(dataContext.getModelMap());

            // 5. 客户端组装阶段
            log.info("=== 第5阶段：AI客户端组装 ===");
            clientNode.doApply(dataContext);

            log.info("AI客户端组装流程完成");

        } catch (Exception e) {
            log.error("AI客户端组装流程失败", e);
            throw new RuntimeException("AI客户端组装流程失败", e);
        }
    }

    /**
     * 重新组装指定的AI客户端
     */
    public void reassembleAiClient(Long clientId) {
        log.info("开始重新组装AI客户端: {}", clientId);

        try {
            // 重新加载数据并组装
            assembleAiClients();
            log.info("AI客户端重新组装完成: {}", clientId);

        } catch (Exception e) {
            log.error("AI客户端重新组装失败: {}", clientId, e);
            throw new RuntimeException("AI客户端重新组装失败: " + clientId, e);
        }
    }
}
