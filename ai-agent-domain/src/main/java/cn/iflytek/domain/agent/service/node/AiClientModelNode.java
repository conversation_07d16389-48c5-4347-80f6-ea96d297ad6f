package cn.iflytek.domain.agent.service.node;

import cn.iflytek.domain.agent.model.AiClientModel;
import io.modelcontextprotocol.client.McpSyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class AiClientModelNode extends AbstractArmorySupport{

    public String doApply()  {
        //执行构建，创建ClientModel对象，使用父类的通用注册方法注册
        return null;
    }
    @Override
    protected String beanName(Long id) {
        return "AiClientModel_" + id;
    }

    /**
     * 创建OpenAiChatModel对象
     *
     * @param modelVO 模型配置值对象
     * @return OpenAiChatModel实例
     */
    private OpenAiChatModel createOpenAiChatModel(AiClientModel modelVO) {
        // 构建OpenAiApi
        OpenAiApi openAiApi = OpenAiApi.builder()
                .baseUrl(modelVO.getBaseUrl())
                .apiKey(modelVO.getApiKey())
                .completionsPath(modelVO.getCompletionsPath())
                .embeddingsPath(modelVO.getEmbeddingsPath())
                .build();

        List<McpSyncClient> mcpSyncClients = new ArrayList<>();
        List<AiClientModel.AIClientModelToolConfigVO> toolConfigs = modelVO.getAiClientModelToolConfigs();
        if (null != toolConfigs && !toolConfigs.isEmpty()) {
            for (AiClientModel.AIClientModelToolConfigVO toolConfig : toolConfigs) {
                Long toolId = toolConfig.getToolId();
                McpSyncClient mcpSyncClient = getBean("AiClientToolMcp_" + toolId);
                mcpSyncClients.add(mcpSyncClient);
            }
        }

        // 构建OpenAiChatModel
        return OpenAiChatModel.builder()
                .openAiApi(openAiApi)
                .defaultOptions(OpenAiChatOptions.builder()
                        .model(modelVO.getModelVersion())
                        .toolCallbacks(new SyncMcpToolCallbackProvider(mcpSyncClients).getToolCallbacks())
                        .build())
                .build();
    }

}
