package cn.iflytek.domain.agent.service.dto;

import cn.iflytek.domain.agent.model.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 军械库数据上下文
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArmoryDataContext {

    /**
     * AI客户端列表
     */
    private List<AiClient> aiClientList;

    /**
     * 系统提示词映射 (ID -> SystemPrompt)
     */
    private Map<Long, AiClientSystemPrompt> systemPromptMap;

    /**
     * 模型配置映射 (ID -> Model)
     */
    private Map<Long, AiClientModel> modelMap;

    /**
     * 顾问配置映射 (ID -> Advisor)
     */
    private Map<Long, AiClientAdvisor> advisorMap;

    /**
     * MCP工具配置映射 (ID -> McpTool)
     */
    private Map<Long, AiClientToolMcp> mcpToolMap;
}
