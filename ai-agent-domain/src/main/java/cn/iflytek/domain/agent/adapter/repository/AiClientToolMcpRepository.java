package cn.iflytek.domain.agent.adapter.repository;

import cn.iflytek.domain.agent.model.AiClientToolMcp;

import java.util.List;

/**
 * AI客户端MCP工具配置仓储接口
 */
public interface AiClientToolMcpRepository {

    /**
     * 保存MCP工具配置
     */
    Long save(AiClientToolMcp mcp);

    /**
     * 根据ID查询MCP工具配置
     */
    AiClientToolMcp findById(Long id);

    /**
     * 根据MCP名称查询MCP工具配置
     */
    AiClientToolMcp findByMcpName(String mcpName);

    /**
     * 根据传输类型查询MCP工具配置列表
     */
    List<AiClientToolMcp> findByTransportType(String transportType);

    /**
     * 查询所有MCP工具配置
     */
    List<AiClientToolMcp> findAll();

    /**
     * 更新MCP工具配置
     */
    boolean update(AiClientToolMcp mcp);

    /**
     * 根据ID删除MCP工具配置
     */
    boolean deleteById(Long id);
}
