package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientSystemPromptRepository;
import cn.iflytek.domain.agent.model.AiClientSystemPrompt;
import cn.iflytek.infrastructure.dao.AiClientSystemPromptMapper;
import cn.iflytek.infrastructure.dao.po.AiClientSystemPromptPO;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端系统提示词仓储实现
 */
@Repository
public class AiClientSystemPromptRepositoryImpl implements AiClientSystemPromptRepository {

    @Resource
    private AiClientSystemPromptMapper promptMapper;

    @Override
    public Long save(AiClientSystemPrompt prompt) {
        AiClientSystemPromptPO promptPO = AiClientSystemPromptPO.builder()
                .promptContent(prompt.getPromptContent())
                .build();

        promptMapper.insert(promptPO);
        return promptPO.getId();
    }

    @Override
    public AiClientSystemPrompt findById(Long id) {
        AiClientSystemPromptPO promptPO = promptMapper.selectById(id);
        if (promptPO == null) {
            return null;
        }

        return convertToAiClientSystemPrompt(promptPO);
    }

    @Override
    public List<AiClientSystemPrompt> findAll() {
        List<AiClientSystemPromptPO> promptPOList = promptMapper.selectAll();
        return promptPOList.stream()
                .map(this::convertToAiClientSystemPrompt)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(AiClientSystemPrompt prompt) {
        AiClientSystemPromptPO promptPO = AiClientSystemPromptPO.builder()
                .id(prompt.getId())
                .promptContent(prompt.getPromptContent())
                .build();

        int result = promptMapper.updateById(promptPO);
        return result > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        int result = promptMapper.deleteById(id);
        return result > 0;
    }

    private AiClientSystemPrompt convertToAiClientSystemPrompt(AiClientSystemPromptPO promptPO) {
        return AiClientSystemPrompt.builder()
                .id(promptPO.getId())
                .promptContent(promptPO.getPromptContent())
                .build();
    }
}
