package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientModelRepository;
import cn.iflytek.domain.agent.model.AiClientModel;
import cn.iflytek.infrastructure.dao.AiClientModelMapper;
import cn.iflytek.infrastructure.dao.AiClientModelToolConfigMapper;
import cn.iflytek.infrastructure.dao.po.AiClientModelPO;
import cn.iflytek.infrastructure.dao.po.AiClientModelToolConfigPO;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端模型配置仓储实现
 */
@Repository
public class AiClientModelRepositoryImpl implements AiClientModelRepository {

    @Resource
    private AiClientModelMapper modelMapper;

    @Resource
    private AiClientModelToolConfigMapper toolConfigMapper;

    @Override
    @Transactional
    public Long save(AiClientModel model) {
        AiClientModelPO modelPO = AiClientModelPO.builder()
                .modelName(model.getModelName())
                .baseUrl(model.getBaseUrl())
                .apiKey(model.getApiKey())
                .completionsPath(model.getCompletionsPath())
                .embeddingsPath(model.getEmbeddingsPath())
                .modelType(model.getModelType())
                .modelVersion(model.getModelVersion())
                .timeout(model.getTimeout())
                .build();

        modelMapper.insert(modelPO);
        Long modelId = modelPO.getId();

        // 保存工具配置
        if (model.getAiClientModelToolConfigs() != null && !model.getAiClientModelToolConfigs().isEmpty()) {
            for (AiClientModel.AIClientModelToolConfigVO toolConfig : model.getAiClientModelToolConfigs()) {
                AiClientModelToolConfigPO toolConfigPO = AiClientModelToolConfigPO.builder()
                        .modelId(modelId)
                        .toolType(toolConfig.getToolType())
                        .toolId(toolConfig.getToolId())
                        .build();
                toolConfigMapper.insert(toolConfigPO);
            }
        }

        return modelId;
    }

    @Override
    public AiClientModel findById(Long id) {
        AiClientModelPO modelPO = modelMapper.selectById(id);
        if (modelPO == null) {
            return null;
        }

        return convertToAiClientModel(modelPO);
    }

    @Override
    public AiClientModel findByModelName(String modelName) {
        AiClientModelPO modelPO = modelMapper.selectByModelName(modelName);
        if (modelPO == null) {
            return null;
        }

        return convertToAiClientModel(modelPO);
    }

    @Override
    public List<AiClientModel> findByModelType(String modelType) {
        List<AiClientModelPO> modelPOList = modelMapper.selectByModelType(modelType);
        return modelPOList.stream()
                .map(this::convertToAiClientModel)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClientModel> findAll() {
        List<AiClientModelPO> modelPOList = modelMapper.selectAll();
        return modelPOList.stream()
                .map(this::convertToAiClientModel)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean update(AiClientModel model) {
        AiClientModelPO modelPO = AiClientModelPO.builder()
                .id(model.getId())
                .modelName(model.getModelName())
                .baseUrl(model.getBaseUrl())
                .apiKey(model.getApiKey())
                .completionsPath(model.getCompletionsPath())
                .embeddingsPath(model.getEmbeddingsPath())
                .modelType(model.getModelType())
                .modelVersion(model.getModelVersion())
                .timeout(model.getTimeout())
                .build();

        int result = modelMapper.updateById(modelPO);

        // 更新工具配置
        toolConfigMapper.deleteByModelId(model.getId());
        if (model.getAiClientModelToolConfigs() != null && !model.getAiClientModelToolConfigs().isEmpty()) {
            for (AiClientModel.AIClientModelToolConfigVO toolConfig : model.getAiClientModelToolConfigs()) {
                AiClientModelToolConfigPO toolConfigPO = AiClientModelToolConfigPO.builder()
                        .modelId(model.getId())
                        .toolType(toolConfig.getToolType())
                        .toolId(toolConfig.getToolId())
                        .build();
                toolConfigMapper.insert(toolConfigPO);
            }
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteById(Long id) {
        // 删除工具配置
        toolConfigMapper.deleteByModelId(id);

        // 删除主记录
        int result = modelMapper.deleteById(id);
        return result > 0;
    }

    private AiClientModel convertToAiClientModel(AiClientModelPO modelPO) {
        // 查询工具配置
        List<AiClientModelToolConfigPO> toolConfigPOList = toolConfigMapper.selectByModelId(modelPO.getId());
        List<AiClientModel.AIClientModelToolConfigVO> toolConfigs = toolConfigPOList.stream()
                .map(toolConfigPO -> {
                    AiClientModel.AIClientModelToolConfigVO toolConfig = new AiClientModel.AIClientModelToolConfigVO();
                    toolConfig.setId(toolConfigPO.getId().intValue());
                    toolConfig.setModelId(toolConfigPO.getModelId());
                    toolConfig.setToolType(toolConfigPO.getToolType());
                    toolConfig.setToolId(toolConfigPO.getToolId());
                    toolConfig.setCreateTime(toolConfigPO.getCreateTime());
                    return toolConfig;
                })
                .collect(Collectors.toList());

        return AiClientModel.builder()
                .id(modelPO.getId())
                .modelName(modelPO.getModelName())
                .baseUrl(modelPO.getBaseUrl())
                .apiKey(modelPO.getApiKey())
                .completionsPath(modelPO.getCompletionsPath())
                .embeddingsPath(modelPO.getEmbeddingsPath())
                .modelType(modelPO.getModelType())
                .modelVersion(modelPO.getModelVersion())
                .timeout(modelPO.getTimeout())
                .aiClientModelToolConfigs(toolConfigs)
                .build();
    }
}
