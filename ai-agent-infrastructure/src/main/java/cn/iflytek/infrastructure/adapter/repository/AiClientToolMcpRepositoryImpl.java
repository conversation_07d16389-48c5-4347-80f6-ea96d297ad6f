package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientToolMcpRepository;
import cn.iflytek.domain.agent.model.AiClientToolMcp;
import cn.iflytek.infrastructure.dao.AiClientToolMcpMapper;
import cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端MCP工具配置仓储实现
 */
@Repository
public class AiClientToolMcpRepositoryImpl implements AiClientToolMcpRepository {

    @Resource
    private AiClientToolMcpMapper mcpMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Long save(AiClientToolMcp mcp) {
        AiClientToolMcpPO mcpPO = convertToAiClientToolMcpPO(mcp);
        mcpMapper.insert(mcpPO);
        return mcpPO.getId();
    }

    @Override
    public AiClientToolMcp findById(Long id) {
        AiClientToolMcpPO mcpPO = mcpMapper.selectById(id);
        if (mcpPO == null) {
            return null;
        }

        return convertToAiClientToolMcp(mcpPO);
    }

    @Override
    public AiClientToolMcp findByMcpName(String mcpName) {
        AiClientToolMcpPO mcpPO = mcpMapper.selectByMcpName(mcpName);
        if (mcpPO == null) {
            return null;
        }

        return convertToAiClientToolMcp(mcpPO);
    }

    @Override
    public List<AiClientToolMcp> findByTransportType(String transportType) {
        List<AiClientToolMcpPO> mcpPOList = mcpMapper.selectByTransportType(transportType);
        return mcpPOList.stream()
                .map(this::convertToAiClientToolMcp)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClientToolMcp> findAll() {
        List<AiClientToolMcpPO> mcpPOList = mcpMapper.selectAll();
        return mcpPOList.stream()
                .map(this::convertToAiClientToolMcp)
                .collect(Collectors.toList());
    }

    @Override
    public boolean update(AiClientToolMcp mcp) {
        AiClientToolMcpPO mcpPO = convertToAiClientToolMcpPO(mcp);
        int result = mcpMapper.updateById(mcpPO);
        return result > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        int result = mcpMapper.deleteById(id);
        return result > 0;
    }

    private AiClientToolMcpPO convertToAiClientToolMcpPO(AiClientToolMcp mcp) {
        AiClientToolMcpPO.AiClientToolMcpPOBuilder builder = AiClientToolMcpPO.builder()
                .id(mcp.getId())
                .mcpName(mcp.getMcpName())
                .transportType(mcp.getTransportType())
                .requestTimeout(mcp.getRequestTimeout());

        // 处理SSE配置
        if (mcp.getTransportConfigSse() != null) {
            builder.sseBaseUri(mcp.getTransportConfigSse().getBaseUri())
                    .sseEndpoint(mcp.getTransportConfigSse().getSseEndpoint());
        }

        // 处理STDIO配置
        if (mcp.getTransportConfigStdio() != null) {
            try {
                String stdioConfigJson = objectMapper.writeValueAsString(mcp.getTransportConfigStdio());
                builder.stdioConfig(stdioConfigJson);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to serialize stdio config", e);
            }
        }

        return builder.build();
    }

    private AiClientToolMcp convertToAiClientToolMcp(AiClientToolMcpPO mcpPO) {
        AiClientToolMcp.AiClientToolMcpBuilder builder = AiClientToolMcp.builder()
                .id(mcpPO.getId())
                .mcpName(mcpPO.getMcpName())
                .transportType(mcpPO.getTransportType())
                .requestTimeout(mcpPO.getRequestTimeout());

        // 构建SSE配置
        if (mcpPO.getSseBaseUri() != null || mcpPO.getSseEndpoint() != null) {
            AiClientToolMcp.TransportConfigSse sseConfig = AiClientToolMcp.TransportConfigSse.builder()
                    .baseUri(mcpPO.getSseBaseUri())
                    .sseEndpoint(mcpPO.getSseEndpoint())
                    .build();
            builder.transportConfigSse(sseConfig);
        }

        // 构建STDIO配置
        if (mcpPO.getStdioConfig() != null) {
            try {
                AiClientToolMcp.TransportConfigStdio stdioConfig = objectMapper.readValue(
                        mcpPO.getStdioConfig(), AiClientToolMcp.TransportConfigStdio.class);
                builder.transportConfigStdio(stdioConfig);
            } catch (JsonProcessingException e) {
                throw new RuntimeException("Failed to deserialize stdio config", e);
            }
        }

        return builder.build();
    }
}
