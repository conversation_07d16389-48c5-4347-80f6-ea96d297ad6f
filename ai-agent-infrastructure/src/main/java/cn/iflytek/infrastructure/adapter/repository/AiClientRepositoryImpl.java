package cn.iflytek.infrastructure.adapter.repository;

import cn.iflytek.domain.agent.adapter.repository.AiClientRepository;
import cn.iflytek.domain.agent.model.AiClient;
import cn.iflytek.infrastructure.dao.AiClientAdvisorRelMapper;
import cn.iflytek.infrastructure.dao.AiClientMapper;
import cn.iflytek.infrastructure.dao.AiClientMcpRelMapper;
import cn.iflytek.infrastructure.dao.po.AiClientAdvisorRelPO;
import cn.iflytek.infrastructure.dao.po.AiClientMcpRelPO;
import cn.iflytek.infrastructure.dao.po.AiClientPO;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * AI客户端仓储实现
 */
@Repository
public class AiClientRepositoryImpl implements AiClientRepository {

    @Resource
    private AiClientMapper aiClientMapper;

    @Resource
    private AiClientAdvisorRelMapper advisorRelMapper;

    @Resource
    private AiClientMcpRelMapper mcpRelMapper;

    @Override
    @Transactional
    public Long save(AiClient aiClient) {
        AiClientPO clientPO = AiClientPO.builder()
                .systemPromptId(aiClient.getSystemPromptId())
                .modelBeanId(Long.valueOf(aiClient.getModelBeanId()))
                .build();

        aiClientMapper.insert(clientPO);
        Long clientId = clientPO.getClientId();

        // 保存顾问关联
        if (aiClient.getAdvisorBeanIdList() != null && !aiClient.getAdvisorBeanIdList().isEmpty()) {
            List<AiClientAdvisorRelPO> advisorRelList = aiClient.getAdvisorBeanIdList().stream()
                    .map(advisorBeanId -> AiClientAdvisorRelPO.builder()
                            .clientId(clientId)
                            .advisorBeanId(Long.valueOf(advisorBeanId))
                            .build())
                    .collect(Collectors.toList());
            advisorRelMapper.batchInsert(advisorRelList);
        }

        // 保存MCP关联
        if (aiClient.getMcpBeanIdList() != null && !aiClient.getMcpBeanIdList().isEmpty()) {
            List<AiClientMcpRelPO> mcpRelList = aiClient.getMcpBeanIdList().stream()
                    .map(mcpBeanId -> AiClientMcpRelPO.builder()
                            .clientId(clientId)
                            .mcpBeanId(Long.valueOf(mcpBeanId))
                            .build())
                    .collect(Collectors.toList());
            mcpRelMapper.batchInsert(mcpRelList);
        }

        return clientId;
    }

    @Override
    public AiClient findByClientId(Long clientId) {
        AiClientPO clientPO = aiClientMapper.selectById(clientId);
        if (clientPO == null) {
            return null;
        }

        return convertToAiClient(clientPO);
    }

    @Override
    public List<AiClient> findAll() {
        List<AiClientPO> clientPOList = aiClientMapper.selectAll();
        return clientPOList.stream()
                .map(this::convertToAiClient)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClient> findByModelBeanId(Long modelBeanId) {
        List<AiClientPO> clientPOList = aiClientMapper.selectByModelBeanId(modelBeanId);
        return clientPOList.stream()
                .map(this::convertToAiClient)
                .collect(Collectors.toList());
    }

    @Override
    public List<AiClient> findBySystemPromptId(Long systemPromptId) {
        List<AiClientPO> clientPOList = aiClientMapper.selectBySystemPromptId(systemPromptId);
        return clientPOList.stream()
                .map(this::convertToAiClient)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public boolean update(AiClient aiClient) {
        AiClientPO clientPO = AiClientPO.builder()
                .clientId(aiClient.getClientId())
                .systemPromptId(aiClient.getSystemPromptId())
                .modelBeanId(Long.valueOf(aiClient.getModelBeanId()))
                .build();

        int result = aiClientMapper.updateById(clientPO);

        // 更新顾问关联
        advisorRelMapper.deleteByClientId(aiClient.getClientId());
        if (aiClient.getAdvisorBeanIdList() != null && !aiClient.getAdvisorBeanIdList().isEmpty()) {
            List<AiClientAdvisorRelPO> advisorRelList = aiClient.getAdvisorBeanIdList().stream()
                    .map(advisorBeanId -> AiClientAdvisorRelPO.builder()
                            .clientId(aiClient.getClientId())
                            .advisorBeanId(Long.valueOf(advisorBeanId))
                            .build())
                    .collect(Collectors.toList());
            advisorRelMapper.batchInsert(advisorRelList);
        }

        // 更新MCP关联
        mcpRelMapper.deleteByClientId(aiClient.getClientId());
        if (aiClient.getMcpBeanIdList() != null && !aiClient.getMcpBeanIdList().isEmpty()) {
            List<AiClientMcpRelPO> mcpRelList = aiClient.getMcpBeanIdList().stream()
                    .map(mcpBeanId -> AiClientMcpRelPO.builder()
                            .clientId(aiClient.getClientId())
                            .mcpBeanId(Long.valueOf(mcpBeanId))
                            .build())
                    .collect(Collectors.toList());
            mcpRelMapper.batchInsert(mcpRelList);
        }

        return result > 0;
    }

    @Override
    @Transactional
    public boolean deleteByClientId(Long clientId) {
        // 删除关联关系
        advisorRelMapper.deleteByClientId(clientId);
        mcpRelMapper.deleteByClientId(clientId);

        // 删除主记录
        int result = aiClientMapper.deleteById(clientId);
        return result > 0;
    }

    private AiClient convertToAiClient(AiClientPO clientPO) {
        // 查询顾问关联
        List<AiClientAdvisorRelPO> advisorRelList = advisorRelMapper.selectByClientId(clientPO.getClientId());
        List<String> advisorBeanIdList = advisorRelList.stream()
                .map(rel -> String.valueOf(rel.getAdvisorBeanId()))
                .collect(Collectors.toList());

        // 查询MCP关联
        List<AiClientMcpRelPO> mcpRelList = mcpRelMapper.selectByClientId(clientPO.getClientId());
        List<String> mcpBeanIdList = mcpRelList.stream()
                .map(rel -> String.valueOf(rel.getMcpBeanId()))
                .collect(Collectors.toList());

        return AiClient.builder()
                .clientId(clientPO.getClientId())
                .systemPromptId(clientPO.getSystemPromptId())
                .modelBeanId(String.valueOf(clientPO.getModelBeanId()))
                .advisorBeanIdList(advisorBeanIdList)
                .mcpBeanIdList(mcpBeanIdList)
                .build();
    }
}
