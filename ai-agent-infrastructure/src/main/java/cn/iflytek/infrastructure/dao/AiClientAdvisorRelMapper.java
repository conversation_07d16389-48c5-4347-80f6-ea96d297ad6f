package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientAdvisorRelPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端与顾问关联Mapper接口
 */
@Mapper
public interface AiClientAdvisorRelMapper {

    /**
     * 插入客户端顾问关联
     */
    int insert(AiClientAdvisorRelPO relPO);

    /**
     * 批量插入客户端顾问关联
     */
    int batchInsert(@Param("list") List<AiClientAdvisorRelPO> relList);

    /**
     * 根据客户端ID查询顾问关联列表
     */
    List<AiClientAdvisorRelPO> selectByClientId(@Param("clientId") Long clientId);

    /**
     * 根据顾问Bean ID查询客户端关联列表
     */
    List<AiClientAdvisorRelPO> selectByAdvisorBeanId(@Param("advisorBeanId") Long advisorBeanId);

    /**
     * 根据客户端ID和顾问Bean ID查询关联
     */
    AiClientAdvisorRelPO selectByClientIdAndAdvisorBeanId(@Param("clientId") Long clientId, @Param("advisorBeanId") Long advisorBeanId);

    /**
     * 根据客户端ID删除所有顾问关联
     */
    int deleteByClientId(@Param("clientId") Long clientId);

    /**
     * 根据顾问Bean ID删除所有客户端关联
     */
    int deleteByAdvisorBeanId(@Param("advisorBeanId") Long advisorBeanId);

    /**
     * 根据客户端ID和顾问Bean ID删除关联
     */
    int deleteByClientIdAndAdvisorBeanId(@Param("clientId") Long clientId, @Param("advisorBeanId") Long advisorBeanId);
}
