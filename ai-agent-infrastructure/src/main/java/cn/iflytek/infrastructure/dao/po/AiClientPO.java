package cn.iflytek.infrastructure.dao.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI客户端持久化对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiClientPO {

    /**
     * 客户端ID
     */
    private Long clientId;

    /**
     * 系统提示词ID
     */
    private Long systemPromptId;

    /**
     * 模型Bean ID
     */
    private Long modelBeanId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
