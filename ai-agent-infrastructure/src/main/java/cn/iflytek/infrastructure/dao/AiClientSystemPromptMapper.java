package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientSystemPromptPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端系统提示词Mapper接口
 */
@Mapper
public interface AiClientSystemPromptMapper {

    /**
     * 插入系统提示词
     */
    int insert(AiClientSystemPromptPO promptPO);

    /**
     * 根据ID查询系统提示词
     */
    AiClientSystemPromptPO selectById(@Param("id") Long id);

    /**
     * 查询所有系统提示词
     */
    List<AiClientSystemPromptPO> selectAll();

    /**
     * 更新系统提示词
     */
    int updateById(AiClientSystemPromptPO promptPO);

    /**
     * 根据ID删除系统提示词
     */
    int deleteById(@Param("id") Long id);
}
