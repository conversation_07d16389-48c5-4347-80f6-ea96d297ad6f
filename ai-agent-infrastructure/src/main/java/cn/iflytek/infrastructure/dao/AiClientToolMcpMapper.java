package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientToolMcpPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端MCP工具配置Mapper接口
 */
@Mapper
public interface AiClientToolMcpMapper {

    /**
     * 插入MCP工具配置
     */
    int insert(AiClientToolMcpPO mcpPO);

    /**
     * 根据ID查询MCP工具配置
     */
    AiClientToolMcpPO selectById(@Param("id") Long id);

    /**
     * 根据MCP名称查询MCP工具配置
     */
    AiClientToolMcpPO selectByMcpName(@Param("mcpName") String mcpName);

    /**
     * 根据传输类型查询MCP工具配置列表
     */
    List<AiClientToolMcpPO> selectByTransportType(@Param("transportType") String transportType);

    /**
     * 查询所有MCP工具配置
     */
    List<AiClientToolMcpPO> selectAll();

    /**
     * 更新MCP工具配置
     */
    int updateById(AiClientToolMcpPO mcpPO);

    /**
     * 根据ID删除MCP工具配置
     */
    int deleteById(@Param("id") Long id);
}
