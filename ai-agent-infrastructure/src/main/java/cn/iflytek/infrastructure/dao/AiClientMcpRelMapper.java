package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientMcpRelPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端与MCP工具关联Mapper接口
 */
@Mapper
public interface AiClientMcpRelMapper {

    /**
     * 插入客户端MCP关联
     */
    int insert(AiClientMcpRelPO relPO);

    /**
     * 批量插入客户端MCP关联
     */
    int batchInsert(@Param("list") List<AiClientMcpRelPO> relList);

    /**
     * 根据客户端ID查询MCP关联列表
     */
    List<AiClientMcpRelPO> selectByClientId(@Param("clientId") Long clientId);

    /**
     * 根据MCP Bean ID查询客户端关联列表
     */
    List<AiClientMcpRelPO> selectByMcpBeanId(@Param("mcpBeanId") Long mcpBeanId);

    /**
     * 根据客户端ID和MCP Bean ID查询关联
     */
    AiClientMcpRelPO selectByClientIdAndMcpBeanId(@Param("clientId") Long clientId, @Param("mcpBeanId") Long mcpBeanId);

    /**
     * 根据客户端ID删除所有MCP关联
     */
    int deleteByClientId(@Param("clientId") Long clientId);

    /**
     * 根据MCP Bean ID删除所有客户端关联
     */
    int deleteByMcpBeanId(@Param("mcpBeanId") Long mcpBeanId);

    /**
     * 根据客户端ID和MCP Bean ID删除关联
     */
    int deleteByClientIdAndMcpBeanId(@Param("clientId") Long clientId, @Param("mcpBeanId") Long mcpBeanId);
}
