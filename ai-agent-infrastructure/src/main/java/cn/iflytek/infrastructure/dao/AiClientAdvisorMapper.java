package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientAdvisorPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端顾问配置Mapper接口
 */
@Mapper
public interface AiClientAdvisorMapper {

    /**
     * 插入顾问配置
     */
    int insert(AiClientAdvisorPO advisorPO);

    /**
     * 根据ID查询顾问配置
     */
    AiClientAdvisorPO selectById(@Param("id") Long id);

    /**
     * 根据顾问名称查询顾问配置
     */
    AiClientAdvisorPO selectByAdvisorName(@Param("advisorName") String advisorName);

    /**
     * 根据顾问类型查询顾问配置列表
     */
    List<AiClientAdvisorPO> selectByAdvisorType(@Param("advisorType") String advisorType);

    /**
     * 查询所有顾问配置
     */
    List<AiClientAdvisorPO> selectAll();

    /**
     * 更新顾问配置
     */
    int updateById(AiClientAdvisorPO advisorPO);

    /**
     * 根据ID删除顾问配置
     */
    int deleteById(@Param("id") Long id);
}
