package cn.iflytek.infrastructure.dao.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI客户端与MCP工具关联持久化对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiClientMcpRelPO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 客户端ID
     */
    private Long clientId;

    /**
     * MCP Bean ID
     */
    private Long mcpBeanId;

    /**
     * 创建时间
     */
    private Date createTime;
}
