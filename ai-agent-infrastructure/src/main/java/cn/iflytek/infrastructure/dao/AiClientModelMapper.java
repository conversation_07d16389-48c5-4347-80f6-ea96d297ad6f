package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientModelPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端模型配置Mapper接口
 */
@Mapper
public interface AiClientModelMapper {

    /**
     * 插入模型配置
     */
    int insert(AiClientModelPO modelPO);

    /**
     * 根据ID查询模型配置
     */
    AiClientModelPO selectById(@Param("id") Long id);

    /**
     * 根据模型名称查询模型配置
     */
    AiClientModelPO selectByModelName(@Param("modelName") String modelName);

    /**
     * 根据模型类型查询模型配置列表
     */
    List<AiClientModelPO> selectByModelType(@Param("modelType") String modelType);

    /**
     * 查询所有模型配置
     */
    List<AiClientModelPO> selectAll();

    /**
     * 更新模型配置
     */
    int updateById(AiClientModelPO modelPO);

    /**
     * 根据ID删除模型配置
     */
    int deleteById(@Param("id") Long id);
}
