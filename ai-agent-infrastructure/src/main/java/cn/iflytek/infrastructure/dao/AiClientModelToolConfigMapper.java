package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientModelToolConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端模型工具配置Mapper接口
 */
@Mapper
public interface AiClientModelToolConfigMapper {

    /**
     * 插入模型工具配置
     */
    int insert(AiClientModelToolConfigPO toolConfigPO);

    /**
     * 根据ID查询模型工具配置
     */
    AiClientModelToolConfigPO selectById(@Param("id") Long id);

    /**
     * 根据模型ID查询工具配置列表
     */
    List<AiClientModelToolConfigPO> selectByModelId(@Param("modelId") Long modelId);

    /**
     * 根据工具类型和工具ID查询配置列表
     */
    List<AiClientModelToolConfigPO> selectByToolTypeAndId(@Param("toolType") String toolType, @Param("toolId") Long toolId);

    /**
     * 查询所有模型工具配置
     */
    List<AiClientModelToolConfigPO> selectAll();

    /**
     * 更新模型工具配置
     */
    int updateById(AiClientModelToolConfigPO toolConfigPO);

    /**
     * 根据ID删除模型工具配置
     */
    int deleteById(@Param("id") Long id);

    /**
     * 根据模型ID删除所有工具配置
     */
    int deleteByModelId(@Param("modelId") Long modelId);
}
