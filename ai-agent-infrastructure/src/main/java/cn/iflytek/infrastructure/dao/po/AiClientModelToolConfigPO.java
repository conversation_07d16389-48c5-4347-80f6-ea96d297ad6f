package cn.iflytek.infrastructure.dao.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * AI客户端模型工具配置持久化对象
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AiClientModelToolConfigPO {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模型ID
     */
    private Long modelId;

    /**
     * 工具类型(mcp/function_call)
     */
    private String toolType;

    /**
     * MCP ID或function call ID
     */
    private Long toolId;

    /**
     * 创建时间
     */
    private Date createTime;
}
