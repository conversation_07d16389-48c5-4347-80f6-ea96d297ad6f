package cn.iflytek.infrastructure.dao;

import cn.iflytek.infrastructure.dao.po.AiClientPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AI客户端Mapper接口
 */
@Mapper
public interface AiClientMapper {

    /**
     * 插入AI客户端
     */
    int insert(AiClientPO aiClientPO);

    /**
     * 根据ID查询AI客户端
     */
    AiClientPO selectById(@Param("clientId") Long clientId);

    /**
     * 查询所有AI客户端
     */
    List<AiClientPO> selectAll();

    /**
     * 根据模型Bean ID查询AI客户端列表
     */
    List<AiClientPO> selectByModelBeanId(@Param("modelBeanId") Long modelBeanId);

    /**
     * 根据系统提示词ID查询AI客户端列表
     */
    List<AiClientPO> selectBySystemPromptId(@Param("systemPromptId") Long systemPromptId);

    /**
     * 更新AI客户端
     */
    int updateById(AiClientPO aiClientPO);

    /**
     * 根据ID删除AI客户端
     */
    int deleteById(@Param("clientId") Long clientId);
}
